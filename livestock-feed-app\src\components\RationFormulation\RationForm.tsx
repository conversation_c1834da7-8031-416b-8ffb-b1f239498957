import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  IconButton,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Alert,
  CircularProgress,
  Chip,
} from '@mui/material';
import {
  Add as AddIcon,
  Delete as DeleteIcon,
  Save as SaveIcon,
  Cancel as CancelIcon,
} from '@mui/icons-material';
import { useForm, Controller, useFieldArray } from 'react-hook-form';
import { yupResolver } from '@hookform/resolvers/yup';
import * as yup from 'yup';
import type { RationFormData, Animal, Ingredient } from '../../types';
import { animalService } from '../../services/animalService';
import { ingredientService } from '../../services/ingredientService';
import { rationService } from '../../services/rationService';

// Validation schema
const rationFormSchema = yup.object({
  name: yup.string().required('Ration name is required').min(2, 'Name must be at least 2 characters'),
  animalId: yup.string().required('Animal selection is required'),
  totalWeight: yup.number()
    .required('Total weight is required')
    .min(1, 'Weight must be at least 1 kg')
    .max(10000, 'Weight cannot exceed 10,000 kg'),
  ingredients: yup.array().of(
    yup.object({
      ingredientId: yup.string().required('Ingredient is required'),
      percentage: yup.number()
        .required('Percentage is required')
        .min(0.1, 'Percentage must be at least 0.1%')
        .max(100, 'Percentage cannot exceed 100%'),
    })
  ).min(1, 'At least one ingredient is required'),
});

interface RationFormProps {
  rationId?: string;
  onSave: (ration: any) => void;
  onCancel: () => void;
}

const RationForm: React.FC<RationFormProps> = ({ rationId, onSave, onCancel }) => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [nutritionalSummary, setNutritionalSummary] = useState<any>(null);
  const [totalCost, setTotalCost] = useState<number>(0);
  const [validationErrors, setValidationErrors] = useState<string[]>([]);

  const {
    control,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<RationFormData>({
    resolver: yupResolver(rationFormSchema),
    defaultValues: {
      name: '',
      animalId: '',
      totalWeight: 100,
      ingredients: [{ ingredientId: '', percentage: 0 }],
    },
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: 'ingredients',
  });

  const watchedIngredients = watch('ingredients');
  const watchedTotalWeight = watch('totalWeight');

  // Load initial data
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        
        // Load animals and ingredients
        const [animalsResponse, ingredientsResponse] = await Promise.all([
          animalService.getAllAnimals(1, 100),
          ingredientService.getAllIngredients(1, 100),
        ]);
        
        setAnimals(animalsResponse.data);
        setIngredients(ingredientsResponse.data);

        // Load existing ration if editing
        if (rationId) {
          const ration = await rationService.getRationById(rationId);
          if (ration) {
            setValue('name', ration.name);
            setValue('animalId', ration.animalId);
            setValue('totalWeight', ration.totalWeight);
            setValue('ingredients', ration.ingredients.map(ing => ({
              ingredientId: ing.ingredientId,
              percentage: ing.percentage,
            })));
          }
        }
      } catch (error) {
        console.error('Failed to load data:', error);
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, [rationId, setValue]);

  // Calculate nutritional summary and cost when ingredients change
  useEffect(() => {
    const calculateNutrition = async () => {
      if (!watchedIngredients || watchedIngredients.length === 0) {
        setNutritionalSummary(null);
        setTotalCost(0);
        return;
      }

      try {
        // Filter out empty ingredients
        const validIngredients = watchedIngredients.filter(
          ing => ing.ingredientId && ing.percentage > 0
        );

        if (validIngredients.length === 0) {
          setNutritionalSummary(null);
          setTotalCost(0);
          return;
        }

        // Create ration ingredients for calculation
        const rationIngredients = validIngredients.map((ing, index) => ({
          id: `temp-${index}`,
          rationId: 'temp',
          ingredientId: ing.ingredientId,
          quantity: (ing.percentage / 100) * watchedTotalWeight,
          percentage: ing.percentage,
        }));

        // Calculate nutrition and cost
        const [nutrition, cost] = await Promise.all([
          rationService.calculateRationNutrition(rationIngredients),
          rationService.calculateRationCost(rationIngredients, watchedTotalWeight),
        ]);

        setNutritionalSummary(nutrition);
        setTotalCost(cost);

        // Validate percentages
        const totalPercentage = validIngredients.reduce((sum, ing) => sum + ing.percentage, 0);
        const errors: string[] = [];
        
        if (Math.abs(totalPercentage - 100) > 0.01) {
          errors.push(`Ingredient percentages must add up to 100%. Current total: ${totalPercentage.toFixed(2)}%`);
        }
        
        setValidationErrors(errors);

      } catch (error) {
        console.error('Failed to calculate nutrition:', error);
      }
    };

    calculateNutrition();
  }, [watchedIngredients, watchedTotalWeight]);

  const onSubmit = async (data: RationFormData) => {
    try {
      setLoading(true);
      
      // Validate total percentage
      const totalPercentage = data.ingredients.reduce((sum, ing) => sum + ing.percentage, 0);
      if (Math.abs(totalPercentage - 100) > 0.01) {
        setValidationErrors([`Ingredient percentages must add up to 100%. Current total: ${totalPercentage.toFixed(2)}%`]);
        return;
      }

      let result;
      if (rationId) {
        result = await rationService.updateRation(rationId, data);
      } else {
        result = await rationService.createRation(data);
      }
      
      onSave(result);
    } catch (error) {
      console.error('Failed to save ration:', error);
      setValidationErrors([error instanceof Error ? error.message : 'Failed to save ration']);
    } finally {
      setLoading(false);
    }
  };

  const addIngredient = () => {
    append({ ingredientId: '', percentage: 0 });
  };

  const removeIngredient = (index: number) => {
    if (fields.length > 1) {
      remove(index);
    }
  };

  const getIngredientName = (ingredientId: string): string => {
    const ingredient = ingredients.find(ing => ing.id === ingredientId);
    return ingredient ? ingredient.name : 'Unknown Ingredient';
  };

  if (loadingData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box component="form" onSubmit={handleSubmit(onSubmit)}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          {rationId ? 'Edit Ration' : 'Create New Ration'}
        </Typography>

        {validationErrors.length > 0 && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {validationErrors.map((error, index) => (
              <div key={index}>{error}</div>
            ))}
          </Alert>
        )}

        <Grid container spacing={3}>
          {/* Basic Information */}
          <Grid item xs={12} md={6}>
            <Controller
              name="name"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Ration Name"
                  fullWidth
                  error={!!errors.name}
                  helperText={errors.name?.message}
                />
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name="animalId"
              control={control}
              render={({ field }) => (
                <FormControl fullWidth error={!!errors.animalId}>
                  <InputLabel>Select Animal</InputLabel>
                  <Select {...field} label="Select Animal">
                    {animals.map((animal) => (
                      <MenuItem key={animal.id} value={animal.id}>
                        {animal.name} ({animal.species})
                      </MenuItem>
                    ))}
                  </Select>
                  {errors.animalId && (
                    <Typography variant="caption" color="error" sx={{ mt: 0.5, ml: 1.5 }}>
                      {errors.animalId.message}
                    </Typography>
                  )}
                </FormControl>
              )}
            />
          </Grid>

          <Grid item xs={12} md={6}>
            <Controller
              name="totalWeight"
              control={control}
              render={({ field }) => (
                <TextField
                  {...field}
                  label="Total Weight (kg)"
                  type="number"
                  fullWidth
                  error={!!errors.totalWeight}
                  helperText={errors.totalWeight?.message}
                />
              )}
            />
          </Grid>

          {/* Ingredients Section */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">Ingredients</Typography>
              <Button
                startIcon={<AddIcon />}
                onClick={addIngredient}
                variant="outlined"
                size="small"
              >
                Add Ingredient
              </Button>
            </Box>

            <TableContainer component={Paper} variant="outlined">
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Ingredient</TableCell>
                    <TableCell align="right">Percentage (%)</TableCell>
                    <TableCell align="right">Quantity (kg)</TableCell>
                    <TableCell align="center">Actions</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {fields.map((field, index) => (
                    <TableRow key={field.id}>
                      <TableCell>
                        <Controller
                          name={`ingredients.${index}.ingredientId`}
                          control={control}
                          render={({ field: ingredientField }) => (
                            <FormControl fullWidth size="small">
                              <Select {...ingredientField} displayEmpty>
                                <MenuItem value="">Select Ingredient</MenuItem>
                                {ingredients.map((ingredient) => (
                                  <MenuItem key={ingredient.id} value={ingredient.id}>
                                    {ingredient.name}
                                  </MenuItem>
                                ))}
                              </Select>
                            </FormControl>
                          )}
                        />
                      </TableCell>
                      <TableCell align="right">
                        <Controller
                          name={`ingredients.${index}.percentage`}
                          control={control}
                          render={({ field: percentageField }) => (
                            <TextField
                              {...percentageField}
                              type="number"
                              size="small"
                              inputProps={{ min: 0, max: 100, step: 0.1 }}
                              sx={{ width: 100 }}
                            />
                          )}
                        />
                      </TableCell>
                      <TableCell align="right">
                        {watchedIngredients[index]?.percentage 
                          ? ((watchedIngredients[index].percentage / 100) * watchedTotalWeight).toFixed(2)
                          : '0.00'
                        }
                      </TableCell>
                      <TableCell align="center">
                        <IconButton
                          onClick={() => removeIngredient(index)}
                          disabled={fields.length === 1}
                          size="small"
                          color="error"
                        >
                          <DeleteIcon />
                        </IconButton>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </Grid>

          {/* Nutritional Summary */}
          {nutritionalSummary && (
            <Grid item xs={12}>
              <Paper variant="outlined" sx={{ p: 2 }}>
                <Typography variant="h6" gutterBottom>
                  Nutritional Summary
                </Typography>
                <Grid container spacing={2}>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="text.secondary">
                      Crude Protein
                    </Typography>
                    <Typography variant="h6">
                      {nutritionalSummary.crudeProtein.toFixed(2)}%
                    </Typography>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="text.secondary">
                      Energy
                    </Typography>
                    <Typography variant="h6">
                      {nutritionalSummary.metabolizableEnergy.toFixed(2)} Mcal/kg
                    </Typography>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="text.secondary">
                      Crude Fiber
                    </Typography>
                    <Typography variant="h6">
                      {nutritionalSummary.crudefiber.toFixed(2)}%
                    </Typography>
                  </Grid>
                  <Grid item xs={6} sm={3}>
                    <Typography variant="body2" color="text.secondary">
                      Total Cost
                    </Typography>
                    <Typography variant="h6" color="primary">
                      ₹{totalCost.toFixed(2)}
                    </Typography>
                  </Grid>
                </Grid>
              </Paper>
            </Grid>
          )}

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end' }}>
              <Button
                onClick={onCancel}
                variant="outlined"
                startIcon={<CancelIcon />}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                variant="contained"
                startIcon={<SaveIcon />}
                disabled={loading || validationErrors.length > 0}
              >
                {loading ? <CircularProgress size={20} /> : (rationId ? 'Update' : 'Create')} Ration
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default RationForm;
