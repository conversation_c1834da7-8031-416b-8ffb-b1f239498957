import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Card,
  CardContent,
  CircularProgress,
  Alert,
  Divider,
  Chip,
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  AttachMoney as AttachMoneyIcon,
  Science as ScienceIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import type { 
  Animal, 
  Ingredient, 
  OptimizationResult, 
  OptimizationConstraints,
  NutritionalRequirement 
} from '../../types';
import { animalService } from '../../services/animalService';
import { ingredientService } from '../../services/ingredientService';
import { optimizationService } from '../../services/optimizationService';

interface OptimizationPanelProps {
  onOptimizationComplete: (result: OptimizationResult) => void;
}

const OptimizationPanel: React.FC<OptimizationPanelProps> = ({ onOptimizationComplete }) => {
  const [animals, setAnimals] = useState<Animal[]>([]);
  const [ingredients, setIngredients] = useState<Ingredient[]>([]);
  const [loading, setLoading] = useState(false);
  const [loadingData, setLoadingData] = useState(true);
  const [error, setError] = useState<string | null>(null);
  
  // Optimization settings
  const [selectedAnimal, setSelectedAnimal] = useState('');
  const [objective, setObjective] = useState<'cost' | 'nutrition'>('cost');
  const [targetNutrient, setTargetNutrient] = useState('protein');
  const [targetWeight, setTargetWeight] = useState(100);
  const [costLimit, setCostLimit] = useState<number | ''>('');
  
  // Nutritional constraints
  const [minProtein, setMinProtein] = useState(16);
  const [maxProtein, setMaxProtein] = useState(20);
  const [minEnergy, setMinEnergy] = useState(2.5);
  const [maxEnergy, setMaxEnergy] = useState(3.2);

  useEffect(() => {
    const loadData = async () => {
      try {
        setLoadingData(true);
        const [animalsResponse, ingredientsResponse] = await Promise.all([
          animalService.getAllAnimals(1, 100),
          ingredientService.getAllIngredients(1, 100),
        ]);
        
        setAnimals(animalsResponse.data);
        setIngredients(ingredientsResponse.data.filter(ing => ing.availability));
      } catch (err) {
        setError(err instanceof Error ? err.message : 'Failed to load data');
      } finally {
        setLoadingData(false);
      }
    };

    loadData();
  }, []);

  const handleOptimize = async () => {
    if (!selectedAnimal) {
      setError('Please select an animal');
      return;
    }

    try {
      setLoading(true);
      setError(null);

      // Create nutritional requirements (mock data for now)
      const requirements: NutritionalRequirement = {
        id: '1',
        species: animals.find(a => a.id === selectedAnimal)?.species || 'cattle',
        ageGroup: 'adult',
        minProtein,
        maxProtein,
        minEnergy,
        maxEnergy,
        minCalcium: 0.6,
        maxCalcium: 1.2,
        minPhosphorus: 0.4,
        maxPhosphorus: 0.8,
      };

      // Create optimization constraints
      const constraints: OptimizationConstraints = {
        nutrientConstraints: [
          { nutrient: 'crudeProtein', min: minProtein, max: maxProtein, target: (minProtein + maxProtein) / 2 },
          { nutrient: 'metabolizableEnergy', min: minEnergy, max: maxEnergy, target: (minEnergy + maxEnergy) / 2 },
        ],
        ingredientConstraints: [],
        costLimit: costLimit ? Number(costLimit) : undefined,
      };

      let result: OptimizationResult;
      
      if (objective === 'cost') {
        result = await optimizationService.optimizeForCost(
          ingredients,
          requirements,
          constraints,
          targetWeight
        );
      } else {
        result = await optimizationService.optimizeForNutrition(
          ingredients,
          requirements,
          constraints,
          targetNutrient,
          targetWeight
        );
      }

      onOptimizationComplete(result);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Optimization failed');
    } finally {
      setLoading(false);
    }
  };

  if (loadingData) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="400px">
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Paper sx={{ p: 3 }}>
      <Typography variant="h6" gutterBottom>
        Ration Optimization Engine
      </Typography>
      <Typography variant="body2" color="text.secondary" paragraph>
        Automatically generate optimized feed formulations based on your constraints and objectives.
      </Typography>

      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      <Grid container spacing={3}>
        {/* Basic Settings */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Basic Settings
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Select Animal</InputLabel>
                    <Select
                      value={selectedAnimal}
                      onChange={(e) => setSelectedAnimal(e.target.value)}
                      label="Select Animal"
                    >
                      {animals.map((animal) => (
                        <MenuItem key={animal.id} value={animal.id}>
                          {animal.name} ({animal.species})
                        </MenuItem>
                      ))}
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControl fullWidth>
                    <InputLabel>Optimization Objective</InputLabel>
                    <Select
                      value={objective}
                      onChange={(e) => setObjective(e.target.value as 'cost' | 'nutrition')}
                      label="Optimization Objective"
                    >
                      <MenuItem value="cost">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AttachMoneyIcon fontSize="small" />
                          Minimize Cost
                        </Box>
                      </MenuItem>
                      <MenuItem value="nutrition">
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <ScienceIcon fontSize="small" />
                          Optimize Nutrition
                        </Box>
                      </MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                {objective === 'nutrition' && (
                  <Grid item xs={12}>
                    <FormControl fullWidth>
                      <InputLabel>Target Nutrient</InputLabel>
                      <Select
                        value={targetNutrient}
                        onChange={(e) => setTargetNutrient(e.target.value)}
                        label="Target Nutrient"
                      >
                        <MenuItem value="protein">Protein</MenuItem>
                        <MenuItem value="energy">Energy</MenuItem>
                      </Select>
                    </FormControl>
                  </Grid>
                )}

                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Target Weight (kg)"
                    type="number"
                    value={targetWeight}
                    onChange={(e) => setTargetWeight(Number(e.target.value))}
                    inputProps={{ min: 1, max: 10000 }}
                  />
                </Grid>

                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Cost Limit (₹)"
                    type="number"
                    value={costLimit}
                    onChange={(e) => setCostLimit(e.target.value ? Number(e.target.value) : '')}
                    placeholder="Optional"
                    inputProps={{ min: 0 }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Nutritional Constraints */}
        <Grid item xs={12} md={6}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Nutritional Constraints
              </Typography>
              
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Min Protein (%)"
                    type="number"
                    value={minProtein}
                    onChange={(e) => setMinProtein(Number(e.target.value))}
                    inputProps={{ min: 0, max: 50, step: 0.1 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Max Protein (%)"
                    type="number"
                    value={maxProtein}
                    onChange={(e) => setMaxProtein(Number(e.target.value))}
                    inputProps={{ min: 0, max: 50, step: 0.1 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Min Energy (Mcal/kg)"
                    type="number"
                    value={minEnergy}
                    onChange={(e) => setMinEnergy(Number(e.target.value))}
                    inputProps={{ min: 0, max: 10, step: 0.1 }}
                  />
                </Grid>
                <Grid item xs={6}>
                  <TextField
                    fullWidth
                    label="Max Energy (Mcal/kg)"
                    type="number"
                    value={maxEnergy}
                    onChange={(e) => setMaxEnergy(Number(e.target.value))}
                    inputProps={{ min: 0, max: 10, step: 0.1 }}
                  />
                </Grid>
              </Grid>
            </CardContent>
          </Card>
        </Grid>

        {/* Available Ingredients Summary */}
        <Grid item xs={12}>
          <Card variant="outlined">
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Available Ingredients
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {ingredients.length} ingredients available for optimization
              </Typography>
              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mt: 1 }}>
                {ingredients.slice(0, 10).map((ingredient) => (
                  <Chip
                    key={ingredient.id}
                    label={ingredient.name}
                    size="small"
                    variant="outlined"
                  />
                ))}
                {ingredients.length > 10 && (
                  <Chip
                    label={`+${ingredients.length - 10} more`}
                    size="small"
                    variant="outlined"
                    color="primary"
                  />
                )}
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Optimization Button */}
        <Grid item xs={12}>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ display: 'flex', justifyContent: 'center' }}>
            <Button
              variant="contained"
              size="large"
              startIcon={loading ? <CircularProgress size={20} /> : <PlayArrowIcon />}
              onClick={handleOptimize}
              disabled={loading || !selectedAnimal}
              sx={{ minWidth: 200 }}
            >
              {loading ? 'Optimizing...' : 'Run Optimization'}
            </Button>
          </Box>
        </Grid>
      </Grid>
    </Paper>
  );
};

export default OptimizationPanel;
